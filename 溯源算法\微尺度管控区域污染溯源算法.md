> 第 58 卷第 6 期
>
> 2024 年 6 月
>
> 浙 江 大 学 学 报 (工学版)
>
> Journal of Zhejiang University (Engineering Science)
>
> Vol.58 No.6 Jun. 2024
>
> DOI: [10.3785/j.issn.1008-973X.2024.06.019](https://doi.org/10.3785/j.issn.1008-973X.2024.06.019)
>
> 基于遗传**-**模式搜索算法的微尺度管控区域大气污染物 **PM_(2.5)** 溯源

董红召¹，金灿¹，唐伟²，佘翊妮¹，林盈盈¹

> (1. 浙江工业大学 智能交通系统研究所，浙江 杭州 310014； 2. 杭州市生态环境科学研究院，浙江 杭州 310014)
>
> 摘 要： 针对微尺度管控区域可能发生的大气污染提出有效的靶向诊断方法−结合高斯烟羽模型和遗传- 模式 搜索算法的大气污染物分布式溯源方法. 将污染源反算模型得到的污染物理论质量浓度与传感器网络观测值的数据对应关系作为目标函数， 使用模式搜索算法嵌入遗传算法加快反算模型的搜索过程， 反算得到污染源强度和位置. 依托杭州市亚运板球场馆大气感知器网络进行实验验证， 监测 2021 年 10 月 PM_(2.5) 质量浓度、气象数据，对所提出的混合式大气污染溯源方法进行实验验证. 实验结果表明： 改进遗传- 模式搜索算法对于多维变量的搜索效果较好， 能快速精准地反算污染源的位置和强度， 可以为微尺度管控区域突发性气体污染防治提供应急决策参考.
>
> 关键词： 源强反算； 遗传-模式搜索算法； 高斯烟羽模型； 微尺度管控； 颗粒物污染溯源
>
> 中图分类号： X 51 文献标志码： A 文章编号： 1008−973X（2024）06−1296−09

Back-calculation of outdoor PM_(2.5) pollutant source around microscale controlled area by genetic-pattern search algorithm

DONG Hongzhao¹, JIN Can¹, TANG Wei², SHE Yini¹, LIN Yingying¹

> (1. *ITS Joint Research Institute*, *Zhejiang University of Technology*, *Hangzhou* 310014, *China*; 2. *Hangzhou Institute of Environment Sciences*, *Hangzhou* 310014, *China*)
>
> **Abstract:** An effective targeted diagnosis method, distributed traceability method for atmospheric pollutants combining Gaussian plume model and genetic-pattern search algorithm, was proposed, aiming at air pollutants that may occur in the micro-scale control area. The corresponding relationship between the calculated pollutant concentration obtained from pollution backcalculation model and the observation value of the monitoring sensor was used as the objective function. Pattern search algorithm was embedded in the genetic algorithm to speed up the search process of the inverse calculation model, then to inversely calculate the intensity and location of the pollution source. A validation experiment was conducted by monitoring the PM_(2.5) mass concentration, meteorology and other data based on the atmospheric sensor data of Hangzhou Asian Games cricket stadium in October 2021. Results showed, compared with other methods, the effect of the improved genetic-pattern search algorithm for multi- dimensional variables was better, and the location and intensity of pollution sources could be calculated more quickly and accurately. This research can provide suggested solution for environmental emergencies of air pollution in micro-scale control regions.
>
> **Key words:** source inversion; genetic-pattern search algorithm; Gaussian plume model; micro-scale control; tracing of particulate matter pollution
>
> 第 19 届亚运会于 2023 年 9 月 23 日—10 月
>
> 8 日在杭州举行. 大气污染物质量浓度的超标对运动员危害严重，易引起人体感官和生理机能的
>
> 不适, 刺激运动员的心血管和呼吸系统，影响运动员的比赛发挥甚至身体健康^(\[[1](#_bookmark13)\]. 因此，在亚运会场馆及周边数公里范围的重要微尺度保障区域，)
>
> 收稿日期： 2023−05−29. 网址： [www.zjujournals.com/eng/article/2024/1008-973X/202406019.shtml](http://www.zjujournals.com/eng/article/2024/1008-973X/202406019.shtml)
>
> 基金项目： 浙江省公益技术研究资助项目（ LGF20F030001）；杭州市农业与社会发展科研资助项目（ 20201203B158）.
>
> 作者简介： 董红召（ 1969—），男，教授，从事智能交通系统、智能环保研究. orcid.org/0000-0001-5905-597X. E-mail： <<EMAIL>>
>
> 通信联系人： 佘翊妮，女，讲师，博士. orcid.org/0009-0006-1670-3271. E-mail: <<EMAIL>>
>
> 第 6 期
>
> 董红召,等：基于遗传-模式搜索算法的微尺度管控区域大气污染物 PM_(2.5) 溯源 \[J\].
>
> 浙江大学学报：工学版, 2024, 58(6): 1296–1304. 1297
>
> 应对突发性大气污染，确定污染源的位置和强度进行靶向治理，对提升城市环境和保护运动员健康都有重大意义.
>
> 国内外大气污染溯源的已有研究主要包括排放源解析和扩散模型法. 排放源解析方法着重通过分析各个区域贡献比例和行业比例的大气污染排放数据进行污染溯源. Qiu 等^(\[[2](#_bookmark14)\] 通过土地覆盖数)据集进行排放源估算，得到的结果是中国的 6 项
>
> 污染物排放的主要来源是农田燃烧； Huang 等^(\[[3](#_bookmark15)\])结合监测数据解析了南宁市一次 PM_(2.5) 污染的成因，发现生物质燃烧和二次无机源污染占比最高；Tian 等^(\[[4](#_bookmark16)\]) 分析了馏分中的化学成分，发现扬尘与柴油车是产生颗粒物污染的前 2 位因素. 上述排放源解析方法，可以将大气污染物排放的来源解析到行业、区域组分中，为污染的治理提供策略，但是他们的缺陷是须进行化学组分的分析， 成本高昂，需要全面的监测数据，数据来源复杂.此外，这些方法致力于确定省际或地区的大范围污染排放来源，对于确定污染源的地理位置有所欠缺，难以适应微尺度的污染来源分析^(\[[5](#_bookmark17)\]). 扩散模型方法为数值模拟方法，考虑风速、风向、地理条件等因素影响和气体扩散方程，建立气体扩散模型，运用数学模型模拟大气污染物的排放、扩散、沉降等过程. Liu 等^(\[[6](#_bookmark18)\]) 使用伴随概率方法建立了流体动力学模型，能识别污染物位置和释放时间； Wang 等^(\[[7](#_bookmark19)\]) 提出几种基于混合遗传算法的 STE 复合代价函数，对污染源位置进行估计；贝叶斯方法和马尔可夫链蒙特卡罗抽样方法也在源强反算中得到验证^(\[8\]). 上述扩散模型法可以快速实现气体泄露源的定位和源强反算，求解效率高. 但当前此类方法多针对有毒气体的泄露和核泄漏问题，其验证方法也基本停留在仿真模拟阶段，无法验证该方法在真实气象条件和地理环境下搜索 PM_(2.5) 污染源的位置和强度的可行性、准确性和即时性.
>
> 高斯烟羽模型常常被用于污染扩散溯源的正
>
> 气体源参数进行了反演求解, 但目前尚不能投入使用； Thomson 等^(\[15\] 发现模拟退火算法和随机搜索算法对源强反算具有良好的鲁棒性. 通过耦合遗传算法和模式搜索算法，结合遗传算法的高效全局搜索能力和模式搜索算法的局部搜索效率， 增强污染溯源模型的搜索效率和准确率.)
>
> 为了解决几十到几百公里的微尺度区域的空气监测和污染溯源问题，填补此类方法的空缺， 对可能出现的突发性大气污染进行准确高效的污染源源头追溯，提出结合高斯烟羽模型和遗传-模式搜索算法的大气污染溯源方法. 通过高斯烟羽模型建立污染源反算模型，将污染源反算模型得到的污染物理论质量浓度与传感器网络观测到的实际质量浓度匹配关系作为适应度函数，使用模式搜索算法嵌入遗传算法加快反算模型的搜索过程，达到快速定位污染源的目的.

# 微尺度管控区域污染溯源算法

1.  大气污染溯源模型建立

> 微尺度管控区域污染溯源问题是源强反算问题. 根据源强分布场中点的理论质量浓度，寻找最佳的源强分布场，使得源强分布场中点的理论质量浓度尽可能接近观测值.
>
> 首先构建物理意义上的大气污染源反算模型，使用高斯烟羽模型作为大气污染物扩散模型.高斯烟羽模型假定在均匀、定常的湍流大气中， 大气污染源下风向污染物浓度分布服从正态分布. 实际大气不满足均匀定常条件，由此可以根据地形、气象条件和排放源几何形状的特殊性推导出一系列高斯扩散模式^(\[16\]. 目前，高斯烟羽模型是广泛应用于模拟大气污染物扩散的模型. 以 PM)_(2.5) 污染源位置为坐标原点，设定流程坐标系， *x* 方向为风向. 高斯烟羽模型的质量浓度（即理论质量浓度 *ρ*_(cal) ）表达式如下：

\[9-11\]

> **q** Ç *y*² å
>
> 向大气扩散模型中 . 也可以通过满足修正系数
>
> PM 污染物的扩散场景里，比如
>
> *ρ*_(cal) =*ρ*(*x, y, z*) = 2π*uσyσz* exp − 2*σy*2 ×
>
> 的方法应用在
>
> 2.5
>
> ® ñ (*z* − *H*)² ô
>
> ñ (*z* + *H*)² ô´
>
> He 等^(\[12\] 使用改进修正因数的方法预测 PM)_(2.5) 的扩散过程. 遗传算法通过模拟生物进化过程搜索
>
> exp −
>
> 2*σ_(z)*2
>
> \+ exp −
>
> 2*σ_(z)*2
>
> *.* (1)
>
> 最优解，可以应用在源项反算的搜索阶段. Akcelik等^(\[13\] 根据扩散方程建立了源强直接优化求解的)方法, 但是这种方法对初值依赖性较大； Ma 等^(\[14\])提出吉洪诺夫与粒子群耦合算法，并对潜在危险
>
> 式中： *ρ*(*x, y, z*) 为监测点在点(*x, y, z*) 处的质量浓度， *q* 为大气污染源排放源强， *u* 为平均风速， *H* 为排放源高度，*σ_(y)* 为气体污染物的水平扩散系数，*σ_(z)*为气体污染物的垂直扩散系数.
>
> 1298 浙 江 大 学 学 报（工学版） 第 58 卷
>
> 扩散系数的取值随着大气稳定度的改变而改变. 大气稳定度是影响污染物在大气中扩散的重要因素，通过风速、太阳辐射强度确定大气稳定度的等级. 如[表](#_bookmark0) [1](#_bookmark0) 所示为大气稳定度的分类，如[表](#_bookmark1) [2](#_bookmark1) 所示为扩散系数^(\[17\]. 表中， *v* 为风速. 通过[表](#_bookmark0) [1](#_bookmark0)、[2](#_bookmark1) 的匹配，确定大气稳定度并获得扩散系数.从而可在污染源位置和源强已知的情况下计算大 气污染物在区域任意位置的质量浓度. 然后，实时采集大气污染物质量浓度数据和气象数据. 在微尺度管控区域布设大气污染感知器网络，监测各个感知器点位 PM)_(2.5) 的质量浓度和风速风向数据. 由气体扩散模型（式（ 1））计算得到点（ *x, y, z* ）的理论质量浓度为 *ρ*_(cal) , 由该位置大气污染感知器 *i* 上传的测量浓度为 *ρ^(i)* . *n* 台大气感知器点位

mea

> 式中：（*x*₀，*y*₀, *z*₀）为污染源位置，*q*₀ 为污染源源强.至此，将污染源的溯源问题通过气体扩散模
>
> 型和数学变换转化为式（3）的解的最优化问题.

2.  模型求解

> 为了达到微尺度管控区域大气污染管控的精确性和即时性，要求溯源用时较短，污染源追溯结果较精确，提出使用遗传-模式搜索算法对模型进行求解. 遗传算法是广泛应用的进化算法，在最新的研究中遗传算法常与其他算法进行耦合增强其搜索性能^(\[[18](#_bookmark29)-[19](#_bookmark30)\]). 在遗传算法模型中，通常选用适应度作为目标函数（式（ 3））的评价的指标参数，适应度越高，解越接近真实值，且适应度要求为非负（零或正数）. 适应度定义如下：
>
> 理论质量浓度与测量质量浓度的误差平方和表达
>
> 式如下：
>
> *n*
>
> exp (−\| *f* (*x, y, z, q*)\| */T* )
>
> *γ* = ∑*n* exp (−\| *f* (*x , y , z , q* )\| */T* )

*i*=1 *i i i i*

*.* (4)

> *f* (*x, y, z, q*) = ∑(*ρ^(i)* − *ρ*_(cal))2*.* (2)

mea

> 式中： *T* 为可调节常数. 可以看出，目标函数的

*i*=1

> 所求 *f* 最小（趋近于零），即 PM_(2.5)
>
> 强最接近实际污染源.
>
> 污染源位置和源
>
> *f* 越小，则适应度 *γ* 越高. 因此，当污染源源强和位置参数取到真实值附近时，适应度最大.
>
> *ρ*_(cal) 由式（1）得出，代入式（2）可以得到
>
> 在求解模型过程中，遗传算法的“选择”步骤
>
> roulette 算法. roulette 算法是常用的选择方
>
> ∑ ï *i *q***₀* Å *1*

*n*

> 中使用
>
> *f* (*x, y, z, q*) =
>
> *i*=1
>
> *ρ*mea − π*uσyσz* exp
>
> − 2 ×
>
> 法，在该方法中，各个个体的选择概率和其适应

. 为了保

> ÇÅ **y* − *y*₀* ã2

\+

*σ_(y)*

> Å **z* − *z*0* ã2ååô₂

*σ_(z)*

*.* (3)

> 度成比例，适应度越大，选中概率也越大
>
> 留适用性强的个体，以父代中适应度大的个体替换适应度小的个体；对适应度最大的个体，直接
>
> 表 **1** 高斯烟羽模型中的大气稳定度分类
>
> Tab.1 Atmospheric stability classification of Gaussian plume model
>
> 白天（太阳辐射） 夜晚（云量）

[TABLE]

> 表 **2** 高斯烟羽模型中的扩散系数
>
> Tab.2 Diffusion coefficient of Gaussian plume model

[TABLE]

> 复制到子代. 通过遗传操作获得新的种群，直到达到设定的迭代次数.
>
> 模式搜索法用于解决最优化问题，在求解函数最大值时，通过找出接近梯度方向的向量、沿着该方向进行变化调整来搜索极值。本研究将遗传算法和模式搜索法结合起来，以遗传算法为主体，再对族群中的最差群体引入模式搜索算法增强搜索能力，从而加快模型的收敛速度。
>
> 如[图](#实-验) [1](#实-验) 所示为将模式搜索算法嵌入遗传算法的流程图. 图中， *P*_(M)、*P*_(C)、*P*_(E) 分别为遗传算法的变异率、交❹率和选择率. *P*_(E) 是对精英子代直接保留和选择的概率. 同时将遗传算法中复制和杂交筛选出的族群适应度与模式搜索算法产生的临时基点适应度进行比较，选择其中的最优点更新 *P*_(E) 和临时基点，得到下一代的新群体和新基点. 遗传- 模式搜索混合优化算法可以弥补遗传算法收敛速度较慢及模式搜索算法初值敏感的不足.
>
> 第 6 期
>
> 董红召,等：基于遗传-模式搜索算法的微尺度管控区域大气污染物 PM_(2.5) 溯源 \[J\].
>
> 浙江大学学报：工学版, 2024, 58(6): 1296–1304. 1299

# 实 验

1.  数据来源及预处理

图 **1** 遗传**-**模式搜索算法流程

族群

更新 *P*_(E) 和临时基点

Y

> 是否最优?

N

舍弃族群

Gen=Gen+1

复制

精英族群

*P*_(E)

轮盘赌算法

临时基点

杂交

> 一般族群
>
> *P*
>
> C
>
> 遗传算法
>
> Gen=0
>
> 模式搜索
>
> 变异
>
> 差族群
>
> 旧的族群
>
> 旧的基点
>
> *P*_(M)
>
> 族群
>
> (*x*₀, *y*₀, *z*₀, *q*₀)

Fig.1 Algorithm process of genetic-pattern search algorithm

> PM₁₀ 质量浓度数据、风速数据、风向数据，上传频率为 15 条/min，如[表 3](#_bookmark3) 所示. 表中，*N* 为数据条数.如[图](#_bookmark5) [3](#_bookmark5) 所示为 6 台感知器的 PM_(2.5) 监测数据

折线图. 图中， *n*_(s) 为时序编号. 各台感知器上传的

1.  数据来源 采用的亚运场馆管控区域为杭

> 州市西湖区亚运会板球场馆区域，在管控区域周边布设大气污染感知器. 大气污染感知器为实验室自主研发，可以采集 PM_(2.5)、PM₁₀ 质量浓度，风速风向，温度等数据并上传服务器端进行数据处理和分析. 将大气污染感知器布置在管控区域地势开阔、建筑物遮挡物较低的道路旁组成杭州市亚运板球场馆大气感知器网络. 所使用的数据区间为大气污染感知网络采集的 2021 年 10 月大气污染感知器网络观测到的数据. 大气污染感知网络布置位置如[图](#_bookmark4) [2](#_bookmark4) 所示，实验期为 2021 年 10 月
>
> 1 日 0 时—2021 年 11 月 1 日 0 时，与亚运会举办时间气候接近. 大气感知器网络数据包括 6 台大气污染感知器上传的 PM _(2. 5) 质量浓度数据、
>
> PM _(2. 5) 质量浓度数据显示， 6 个监测点位的 PM_(2.5) 质量浓度变化较大，有明显的峰谷现象，高值达到近 500 μg/m³，存在 PM_(2.5) 污染现象.
>
> ![](media/image2.jpeg)
>
> 图 **2** 大气污染感知器分布
>
> Fig.2 Distribution of air pollution sensors

表 **3** 大气感知网络中感知器位置及数据量

Tab.3 Sensors locations and data volume of atmospheric perceptron network

[TABLE]

> 1300 浙 江 大 学 学 报（工学版） 第 58 卷

450

400

> *ρ*_(mea)/(μg·m⁻³)

350

300

250

200

150

100

50

0

> 1 2 3 4 5 6 7 8 9 10
>
> *n*_(s)/10⁴

1)  1号感知器

450

400

> *ρ*_(mea)/(μg·m⁻³)

350

300

250

200

150

100

50

0

> 1 2 3 4 5 6 7 8 9 10
>
> *n*_(s)/10⁴

2)  2号感知器

450

400

> *ρ*_(mea)/(μg·m⁻³)

350

300

250

200

150

100

50

0

> 1 2 3 4 5 6 7 8 9 10
>
> *n*_(s)/10⁴

3)  3号感知器

450

400

> *ρ*_(mea)/(μg·m⁻³)

350

300

250

200

150

100

50

0

> 1 2 3 4 5 6 7 8 9 10
>
> *n*_(s)/10⁴

4)  4号感知器

450

400

> *ρ*_(mea)/(μg·m⁻³)

350

300

250

200

150

100

50

> 1 2 3 4 5 6 7 8 9 10
>
> *n*_(s)/10⁴

5)  5号感知器

450

400

> *ρ*_(mea)/(μg·m⁻³)

350

300

250

200

150

100

50

0

> 1 2 3 4 5 6 7 8 9 10
>
> *n*_(s)/10⁴

6)  6号感知器

图 **3** 大气感知器网络中各感知器的 **PM_(2.5)** 质量浓度监测数据

> Fig.3 PM_(2.5) mass concentration monitoring data of atmospheric perceptron network

1.  数据处理 [表](#_bookmark3) [3](#_bookmark3) 中各感知器采集的数据量庞大且无法直接应用，因此须对感知器上传的风速风向数据及 PM_(2.5) 质量浓度数据进行预处理.首先， 对 6 台大气污染感知器的数据进行时间序 列的匹配；其次，对 PM_(2.5) 质量浓度数据异常值进行处理. PM_(2.5) 质量浓度数据出现异常值的主要原因及处理方式如下. 1）由于颗粒物传感器的特性，监测设备偶尔会因外界环境的变化产生突发异常数据，如暴雨天气下， PM_(2.5) 质量浓度数据偶尔会跳跃至负数或超出量程显示示数为 9 999，此类异常数据直接在预处理过程中予以剔除. 2）设备进行维护时会出现短暂的连续性数据缺失，针对这种异常值，直接剔除过于奢侈，使用多层感知器神经网络 (multi-layer perceptron，MLP) 的方法对数据进行补全. 为了减少计算负担，并满足数据须覆盖 10 月全月较长时段的要求，最终选取全月

> 130 000 条数据进行反演.
>
> 使用多层感知器神经网络进行时序缺失数据的补全， ML P 是一种前馈人工神经网络 模型^(\[20\])，常见于数据预测和数据补全. 输入 PM₁₀质量浓度、温度、湿度、风速风向数据作为协变量，
>
> 应用 MLP 预测方法对 PM_(2.5) 质量浓度数据中 2%缺失数据段进行补全，如[图](#_bookmark6) [4](#_bookmark6) 所示为 1 号感知器数据缺失时段预处理后的 PM_(2.5) 质量浓度数据。

1.  求解结果

> 在实验期间的亚运会板球场馆大气质量监测范围内，因为存在建筑施工、建筑物料搬运、地面垃圾焚烧等活动，污染物质量浓度远高于其他区域的. 以污染高值区域作为 PM_(2.5) 污染源原点，根据《污染源源强核算技术指南》（HJ884-2018）^(\[21\])核算得到 PM_(2.5) 污染源源强为 1.39 g/s，即污染源为（0，0，0，1.39）.
>
> 在实验期间，以某时刻实验数据为例，该时刻大气稳定度为 D，风向为东偏南 45°，风力为 2 m/s，各感知器 PM_(2.5) 质量浓度观测值如[表](#_bookmark8) [4](#_bookmark8) 所示.
>
> 利用遗传- 模式搜索算法对目标函数（式（ 3）） 进行寻优，结果如[表](#_bookmark9) [5](#_bookmark9) 所示.
>
> 当循环次数为 400 次时，输出结果已经较好， 且即使初值的选取偏离目标值较大，也能搜索到与期望值较接近的结果.
>
> 由于所使用的遗传算法可能出现算例抽样误差，使用 10 月全月 780 000 条 PM_(2.5) 质量浓度数据
>
> 第 6 期
>
> 董红召,等：基于遗传-模式搜索算法的微尺度管控区域大气污染物 PM_(2.5) 溯源 \[J\].
>
> 浙江大学学报：工学版, 2024, 58(6): 1296–1304. 1301

0.30

0.25

0.20

> *ρ*_(pre)/(mg·m⁻³)

0.15

0.10

0.05

0

0 100 200 300

②

①-⑥ MLP 方法补全数据

①

⑤

③ ④

⑥

> 400 550 650 750
>
> *n*_(s)

500

400

300

> fs

200

100

0

500

> −500 −500
>
> *f*_(s)
>
> ![](media/image11.jpeg)450

0

0

> 400
>
> 350
>
> 300
>
> 250
>
> 200
>
> 150
>
> 100
>
> 50
>
> 0
>
> 500
>
> 图 **4** 预处理后的 **PM_(2.5)** 质量浓度数据
>
> Fig.4 Pre-processed data of PM_(2.5) mass concentration
>
> 表 **4** 传感器位置及 **PM_(2.5)** 质量浓度观测值
>
> Tab.4 Sensors locations and PM_(2.5) mass concentration moni- toring values

500

400

300

200

100

> *y*/m

0

−100

−200

−300

−400

−500

> \(a\) 污染源搜索结果曲面图
>
> *f*_(s)
>
> 450
>
> 400
>
> 350
>
> 300
>
> 250
>
> 200
>
> 150
>
> 100
>
> 50
>
> 0

−500−400−300−200−100

[TABLE]

> 0
>
> *x*/m
>
> 100 200 300 400 500
>
> 对污染源位置进行搜索，并将搜索次数结果拟合曲面显示在管控区域坐标系中，得到可视化的污染源定位结果，如[图](#_bookmark7) [5](#_bookmark7) 所示. 图中， *f*_(s) 为搜索次数.显然，搜索结果较集中，且接近真实污染源位置
>
> （ 0,0），（ 0,0）位置附近搜索次数远高于管控区域内其他位置. 在 *x* 方向上，搜索结果偏离不超过 5 m，*y* 方向上偏离不超过 30 m.

2.  比较分析

> 为了体现*遗传- 模式搜索算*法在亚运会场馆大气污染物溯源问题上的优越性，与其他大气污染溯源方法进行对比分析. 适用于微尺度区域污染溯源的方法较少，其中基于机器学习方法进行气体污染溯源模型求解是目前较主流的方法. 选取遗传- Nelder Mead 单纯形混合（ GA-NM）^(\[[22](#_bookmark33)\] 算)法、粒子群与单纯形耦合（ PSO-NM）^(\[[23](#_bookmark34)\] 算法和遗)
>
> \(b\) 污染源搜索结果平面图
>
> 图 **5 10** 月污染源位置搜索热点
>
> Fig.5 Search hotspot of October pollution source location
>
> 传-模式搜索（GA-PS）算法进行对比. GA-NM 算法是常见的气体污染溯源模型求解方法， 以 GA 算法为主体, 采用群体分类的方式构建，其在毒气泄漏领域的应用较广泛，已经在仿真实验中得到了验证^(\[[22](#_bookmark33)\]. PSO-NM 方法，是在 PSO 算法执行后,再执行 NM 算法寻优, 直到满足收敛条件，其在美国奥尼尔镇经典草场 SO)₂ 泄漏实验中得到过验证，效果较好^(\[23\])，且有研究表明该方法在成功率和计算均值方面较有优势^(\[24\]. 因此选用这 2 种主流方法与 GA-PS 算法在源强反演中开展对比研究.仍以上述场景为实验对象，真实污染源)
>
> （*x*₀*, y*₀*, z*₀*, q*₀） =（ 0, 0, 0, 1.39），选取 10 月 PM_(2.5)
>
> 质量浓度数据及风速风向数据共 130 000 条数据
>
> 作为输入. 各算法循环次数均为 1 000 次，搜索结果如[表](#_bookmark10) [6](#_bookmark10) 所示. 其中，源强取搜索结果的平均值， 位置搜索结果取全月搜索结果的几何形心. 表中， *t* 为响应时间， *q*₀ 为源强， *x*₀、*y*₀ 为位置变量， Δ*q*₀ 为源强相对误差，Δ*x*₀、Δ*y*₀ 为位置变量绝对误差.
>
> 对其中位置变量（ *x*₀， *y*₀）的结果进行研究分析，将各优化算法搜索次数拟合曲面的热点等高
>
> 1302 浙 江 大 学 学 报（工学版） 第 58 卷
>
> 表 **6** 各算法响应时间和搜索准确度比较
>
> Tab.6 Comparison of response time and search accuracy of various algorithms
>
> 算法 *t*/s *q*0/（g·s−1） *x*₀/m *y*₀/m Δ*q*₀/% Δ*x*₀/m Δ*y*₀/m GA-PS 2.44 1.29 −7.1 4.8 7.2 −7.1 4.8
>
> GA-NM 2.57 1.73 −21.1 4.3 24.5 −21.1 4.3
>
> PSO-NM 2.65 1.65 41.1 −35.0 18.7 41.1 −35.0
>
> 线图显示在管控区域坐标系中，如[图](#_bookmark11) [6](#_bookmark11) 所示.
>
> 分析[图](#_bookmark11) [6](#_bookmark11) 及[表](#_bookmark10) [6](#_bookmark10) 中各算法的比较结果，结论如下.

1)  运算时间. 由于使用模式搜索增强了局部

250

200

(−7.1, 4.8)

150

100

50

0

> *y*/m

−50

−100

−150

−200

> −250
>
> −250−200−150−100 −50
>
> 0 50
>
> *x*/m
>
> 100 150 200 250
>
> *f*_(s)
>
> ![](media/image14.jpeg)1 400
>
> 1 200
>
> 1 000
>
> 800
>
> 600
>
> 400
>
> 200
>
> 0
>
> −200
>
> 搜索的能力，遗传-模式搜索的运算速度相比较单独使用遗传算法提升较大，相对其他典型优化算法在搜索速度上也有一定提升. GA-PS 算法平均运算时间为 2.44 s， 比 GA-NM 算法（ 2.57 s） 和 PSO-NM 算法（2.65 s）速度更快，在输入样本庞大的时候，其具有搜索速度较快的优势.

250

200

150

100

50

0

> *y*/m

1)  GA-PS

> ![](media/image15.png)*f*s 1 600

(−21.1, −4.3)

> 1 400
>
> 1 200
>
> 1 000
>
> 800
>
> 600

2)  污染源源强反算. 对于源强 *q*₀

> 解的搜索，
>
> −50
>
> −100

400

> 遗传- 模式搜索算法的效果较好. 在算例中， *q*₀ 相
>
> 对误差为 7.2%， 而 GA-NM 算法（ 24.5%） 和 PSO- NM 算法（41.1%）的污染源源强误差较大，几乎不能获得较为准确的源强.
>
> −150
>
> −200
>
> −250
>
> −250−200−150−100 −50
>
> 0 50
>
> *x*/m
>
> 100 150 200 250

200

0

−200

3)  污染源位置搜索. 污染源位置的搜索精确度是微尺度污染溯源的重点. 对于污染源位置

> （ *x*₀， *y*₀）的搜索，由[图](#_bookmark11) [6](#_bookmark11) 可以看出，各算法对污染源反演的位置搜索结果的热点都较集中， GA-PS、 GA-NM、PSO-NM 算法的搜索结果分别为（−7.1 m,
>
> 4.8 m）、（−21.1 m， 4.5 m）、（41.1 m， −35.0 m）. GA- PS 算法的搜索效果最好， 其 *x* 方向位置变量的搜索偏差不超过 10 m， *y* 方向位置变量搜索偏差不超过 5 m，位置搜索精度最高， 在 3 种优化算法中表现最好. 可以满足亚运场馆周边污染源的搜索

250

200

150

100

50

0

> *y*/m

−50

−100

−150

−200

> −250

2)  GA-NM

> *f*_(s)
>
> ![](media/image17.png)1 400

(41.4, −35.0)

> 1 200
>
> 1 000
>
> 800
>
> 600
>
> 400
>
> 200
>
> 0
>
> 要求.

4)  算法稳定性. 为了比较算法的稳定性，选取同一组数据，在多次实验中， *x*、*y*、源强参数在约 500 次循环达到收敛. 为了对比算法性能，使用

&nbsp;

3.  种算法进行 20 [次运算，每次运算的循环次数均](#_bookmark12)为 1 000 次. 输出的误差值如[图](#_bookmark12) [7](#_bookmark12) 所示， GA-PS、 GA-NM、PSO-NM 算法的 *x* 方向位置变量绝对误差范围分别为 6.5~11.7 、 16.8~24.1 、 40.8~

> 42.8 m，显然， PSO-NM 算法对 *x* 方向位置参数的搜索稳定性最好. 在 *y* 方向位置变量的搜索稳定性上， PSO-NM 算法也优于另外 2 个算法. PSO-

−250−200−150−100 −50 0 50 100 150 200 250

*x*/m

3)  PSO-NM

> 图 **6** 各算法搜索热点对比
>
> Fig.6 Search-hotspot comparison of various algorithms
>
> NM 算法对位置参数的搜索稳定性最好，而对于源强的反算，3 种算法的稳定性没有明显优劣.
>
> 由上述对比可以得出，在微尺度管控区域大气污染物溯源上， GA-PS 算法在搜索速度、污染源位置搜索准确度、污染源源强反算准确度等方面具有明显的优势. 实验结果显示，PSO-NM 算法
>
> 第 6 期
>
> 董红召,等：基于遗传-模式搜索算法的微尺度管控区域大气污染物 PM_(2.5) 溯源 \[J\].
>
> 浙江大学学报：工学版, 2024, 58(6): 1296–1304. 1303
>
> 0

45

40

35

30

25

20

15

10

> 5

GA-PS

GA-NM PSO-NM

> Δ*x*₀/m
>
> 1 3 5 7 9 11
>
> 13 15 17 19 21
>
> 值远离期望值，也能得到较好的结果.
>
> \(3\) 所提算法适用于多维变量的搜索，相较其他优化算法具有搜索时间短、污染源位置搜索精确度高、污染源源强反算准确度高等优势，可以满足亚运会管控区域或其他类似物理场景中对于突发性气体污染防治的应急决策需要.
>
> （ 4） 本研究不足在于无法对同时作用的多个
>
> 实验编号

1)  *x*变量误差比较

> 污染源进行溯源，下一步计划使用本研究的思路和方法，通过构造叠加的大气污染源扩散模型， 进行多污染源溯源的研究.
>
> 参考文献 **(References):**

40

35

30

25

20

15

10

> 5

GA-PS

GA-NM PSO-NM

> 0
>
> Δ*y*₀/m
>
> 2 4 6 8 10
>
> 12 14 16 18 20

\[1\]

> CHEN Y, LUO X, ZHAO Z, et al. Summer-winter differences of PM2.5 toxicity to human alveolar epithelial cells (A549) and the
>
> 实验编号

2)  *y*变量误差比较

35

30

25

20

15

10

> 5

GA-PS

GA-NM PSO-NM

0

> Δ*q*₀/%

\[2\]

\[3\]

> roles of transition metals \[J\]. **Ecotoxicology and Environmental Safety**, 2018, 165(9): 505–509.
>
> QIU X, DUAN L, CHAI F, et al. Deriving high-resolution emission inventory of open biomass burning in China based on satellite observations \[J\]. **Environmental Science and Technology**, 2016, 50(21): 11779–11779.
>
> HUANG J, CHEN Z, ZHOU B, et al. Cause analysis of PM2.5 pollution during the COVID-19 lockdown in Nanning, China \[J\].
>
> **Scientific Reports**, 2021, (11): 11119.
>
> 2 4 6 8 10 12 14 16 18 20
>
> 实验编号

3)  源强相对误差比较

> 图 **7** 不同算法坐标反演误差稳定性比较
>
> Fig.7 Comparison of stability in coordinate inversion of different algorithms
>
> 与 GA-PS 算法相比，没有受到遗传算法操作过程中随机性的影响，显然在求解稳定性方面具有优势， 但 PSO-NM 算法很难准确求解出污染源的源强数值，而且对污染源位置的搜索精准度也远不如 GA-PS 算法的.

# 3 结 论

1)  结合气体扩散方程和遗传- 模式搜索算法，构造了亚运场馆大气污染物 PM_(2.5) 的溯源算法. 有别于传统的仿真验证，在亚运会板球场馆进行应用，证明了该方法在真实气象条件和地理环境下搜索 PM_(2.5) 污染源的位置和强度的可行性、准确性和即时性.

2)  所提溯源方法在污染源位置多维变量搜索的应用上较为精准，且不受初值影响，即使初

> \[4\]
>
> \[5\]
>
> \[6\]
>
> \[7\]
>
> \[8\]
>
> \[9\]
>
> \[10\]
>
> TIAN Y, HARRISON R M, FENG Y, et al. Size-resolved source apportionment of particulate matter from a megacity in northern China based on one-year measurement of inorganic and organic components \[J\]. **Environmental Pollution**, 2021, 289: 117932. HAUPT S E, HAUPT R L, YOUNG G S. A mixed integer genetic algorithm used in biological and chemical defense applications \[J\]. **Soft Computing**, 2011, 15(1): 51–59.
>
> LIU X, ZHAI Z. Inverse modeling methods for indoor airborne pollutant tracking: literature review and fundamentals \[J\]. **Indoor Air**, 2007, 17(6): 419–438.
>
> WANG Y, HUANG H, HUANG L, et al. Source term estimation of hazardous material releases using hybrid genetic algorithm with composite cost functions \[J\]. **Engineering Applications of Artificial Intelligence**, 2018, 75(11): 102–113.
>
> MONACHE L D, LUNDQUIST J K, KOSOVIC B, et al.
>
> Bayesian inference and Markov Chain Monte Carlo sampling to reconstruct a contaminant source on a continental scale \[J\]. **Journal of Applied Meteorology and Climatology**, 2008, 47(10): 2600–2613.
>
> LEE B, CHO S, LEE S, et al. Development of a smoke dispersion forecast system for Korean forest fires \[J\]. **Forests**, 2019, 10(3): 219.
>
> ZHANG Q, GUO R, ZHANG C, et al. Radioactive airborne
>
> 1304 浙 江 大 学 学 报（工学版） 第 58 卷
>
> \[11\]
>
> \[12\]
>
> \[13\]
>
> \[14\]
>
> \[15\]
>
> \[16\]
>
> \[17\]
>
> \[18\]
>
> effluents and the environmental impact assessment of CAP1400 nuclear power plant under normal operation \[J\]. **Nuclear Engineering and Design**, 2014, 280(12): 579–585.
>
> BANAN Z, GERNAND J M. Emissions of particulate matter due to Marcellus Shale gas development in Pennsylvania: mapping the implications \[J\]. **Energy Policy**, 2021, 148(1): 111979.
>
> HE P, ZHENG B, ZHENG J, et al. Urban PM2.5 diffusion analysis based on the improved Gaussian smoke plume model and support vector machine \[J\]. **Aerosol and Air Quality Research**, 2018, 18(12): 3177–3186.
>
> AKCELIK V, BIROS G, GHATTAS O, et al. A variational finite element method for source inversion for convective-diffusive transport \[J\]. **Finite Elements in Analysis and Design**, 2003, 39(8): 683–705.
>
> MA D, TAN W, ZHANG Z, et al. Parameter identification for continuous point emission source based on Tikhonov regularization method coupled with particle swarm optimization algorithm \[J\]. **Journal of Hazardous Materials**, 2017, 325: 239–250.
>
> THOMSON L C, HIRST B, GIBSON G, et al. An improved algorithm for locating a gas source using inverse methods \[J\]. **Atmospheric Environment**, 2007, 41(6): 1128–1134.
>
> 梁俊丽, 孔维华, 费文华, 等. 基于复杂地形的高斯烟羽模型改
>
> 进 \[J\]. [环境工程学报](https://doi.org/10.12030/j.cjee.201501066), 2016, 10(6): 5.
>
> LIANG Junli, KONG Weihua, FEI Wenhua, et al. lmprovement of Gaussian plume model in complex terrain \[J\]. **Chinese Journal of Environmental Engineering**, 2016, 10(6): 5.
>
> GROSSEL S S. Chemical process safety: fundamentals with applications, 2nd edition (2002) \[J\]. **Journal of Loss Prevention in the Process Industries**, 2002, 15(15): 565–566.
>
> 胡鸿昊, 李秀娟, 于俊锋, 等. 基于耦合模拟的污水管网入流入
>
> 渗定量识别 \[J\]. 浙江大学学报:工学版, 2022, 56(11): 8.
>
> HU Honghao, LI Xiujuan, YU Junfeng, et al. Quantitative identification of inflow and infiltration of sanitary sewer system
>
> \[19\]
>
> \[20\]
>
> \[21\]
>
> \[22\]
>
> \[23\]
>
> \[24\]
>
> based on coupling simulation \[J\]. **Journal of Zhejiang University: Engineering Science**, 2022, 56(11): 8.
>
> 向胜涛, 王达. 基于改进量子遗传算法的模型交互修正方法
>
> \[J\]. 浙江大学学报:工学版, 2022, 56(1): 11.
>
> XIANG Shengtao, WANG Da. Model interactive modification method based on improved quantum genetic algorithm \[J\]. **Journal of Zhejiang University: Engineering Science**, 2022, 56(1): 11.
>
> 谢劭峰, 曾印, 张继洪, 等. 基于 MLP 神经网络的大气加权平
>
> 均温度模型 \[J\]. 大地测量与地球动力学, 2022, 42(11): 1105–1110.
>
> XIE Shaofeng, ZENG Yin, ZHANG Jihong, et al. Atmospheric weighted mean temperature model based on mlp neural network \[J\]. **Journal of Geodesy and Geodynamics**, 2022, 42(11): 1105–1110.
>
> 生态环境部. 污染源源强核算技术指南准则: HJ884—2018 \[S\].
>
> 北京: 中国环境科学出版社, 2018.
>
> 张建文, 王煜薇, 郑小平, 等. 基于混合遗传- Nelder Mead 单纯形算法的源强及位置反算 \[J\]. 系统工程理论与实践, 2011, 31(8): 1581–1587.
>
> ZHANG Jianwen, WANG Yuwei, ZHENG Xiaoping, et al. Back- calculation of source strength and position by a hybrid genetic- Nelder Mead simplex algorithm \[J\]. **Systems Engineering: Theory and Practice**, 2011, 31(8): 1581–1587.
>
> 沈泽亚, 郎建垒, 程水源, 等. 典型耦合优化算法在源项反演中
>
> 的对比研究 \[J\]. [中国环境科学](https://doi.org/10.3969/j.issn.1000-6923.2019.08.010), 2019, 39(8): 3207–3214.
>
> SHEN Zeya, LANG Jianlei, CHENG Shuiyuan, et al. Comparative and study on the application of typical hybrid algorithms in source parameter inversions \[J\]. **China Environmental Science**, 2019, 39(8): 3207–3214.
>
> VAKIL BAGHMISHEH M T, PEIMANI M, SADEGHI M H, et
>
> al. A hybrid particle swarm-nelder-mead optimization method for crack detection in cantilever beams \[J\]. **Applied Soft Computing**, 2012, 12(8): 2217–2226.
