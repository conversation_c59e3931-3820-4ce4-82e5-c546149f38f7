"""
污染溯源系统使用示例
演示如何使用完整的AI污染溯源系统
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import random

# 导入系统模块
from pollution_tracing_system import PollutionTracingSystem, SystemConfig
from genetic_pattern_search import GAParameters
from spatiotemporal_analysis import SpatialEvent


def generate_sample_monitoring_data(num_stations: int = 10,
                                  num_records_per_station: int = 24,
                                  add_pollution_event: bool = True) -> dict:
    """
    生成示例监测数据

    Args:
        num_stations: 监测站数量
        num_records_per_station: 每个站点的记录数
        add_pollution_event: 是否添加污染事件

    Returns:
        监测数据字典
    """
    # 生成监测站位置
    stations = []
    for i in range(num_stations):
        x = random.uniform(-800, 800)
        y = random.uniform(-800, 800)
        z = random.uniform(2, 10)
        stations.append({
            'station_id': f'station_{i+1:02d}',
            'x': x, 'y': y, 'z': z,
            'station_type': random.choice(['national', 'township', 'micro'])
        })

    # 生成时间序列
    start_time = datetime.now() - timedelta(hours=num_records_per_station)
    time_points = [start_time + timedelta(hours=i) for i in range(num_records_per_station)]

    # 生成监测数据
    monitoring_data = []

    # 污染源位置（如果添加污染事件）
    pollution_source_x, pollution_source_y = 100, 200
    pollution_intensity = 5.0 if add_pollution_event else 0.0  # 增加污染强度
    
    for station in stations:
        for time_point in time_points:
            # 计算到污染源的距离
            distance = np.sqrt((station['x'] - pollution_source_x)**2 + 
                             (station['y'] - pollution_source_y)**2)
            
            # 基础浓度 + 污染源影响
            base_pm25 = random.uniform(20, 40)
            base_pm10 = random.uniform(40, 80)
            base_o3 = random.uniform(50, 100)
            base_no2 = random.uniform(20, 60)
            base_vocs = random.uniform(10, 50)
            
            if add_pollution_event and distance < 800:  # 扩大影响范围
                # 添加污染源影响（距离越近影响越大）
                influence = pollution_intensity * np.exp(-distance / 300)
                base_pm25 += influence * 80   # 增加影响强度
                base_pm10 += influence * 120
                base_no2 += influence * 60
                base_vocs += influence * 150
                base_o3 += influence * 40     # 添加O3影响
            
            # 添加随机噪声
            pm25 = max(0, base_pm25 + random.gauss(0, 5))
            pm10 = max(0, base_pm10 + random.gauss(0, 10))
            o3 = max(0, base_o3 + random.gauss(0, 15))
            no2 = max(0, base_no2 + random.gauss(0, 8))
            vocs = max(0, base_vocs + random.gauss(0, 20))
            
            # 气象数据
            temperature = random.uniform(15, 25)
            humidity = random.uniform(40, 80)
            wind_speed = random.uniform(1, 5)
            wind_direction = random.uniform(0, 360)
            pressure = random.uniform(1000, 1020)
            
            # 随机添加一些缺失值
            if random.random() < 0.05:  # 5%的缺失率
                pm25 = None
            if random.random() < 0.03:
                pm10 = None
            
            monitoring_data.append({
                'timestamp': time_point.isoformat(),
                'station_id': station['station_id'],
                'x': station['x'],
                'y': station['y'],
                'z': station['z'],
                'pm25': pm25,
                'pm10': pm10,
                'o3': o3,
                'no2': no2,
                'vocs': vocs,
                'temperature': temperature,
                'humidity': humidity,
                'wind_speed': wind_speed,
                'wind_direction': wind_direction,
                'pressure': pressure
            })
    
    # 转换为DataFrame
    df = pd.DataFrame(monitoring_data)
    
    return {
        'national': df[df['station_id'].str.contains('01|02|03')],  # 前3个作为国控站
        'township': df[df['station_id'].str.contains('04|05|06|07')],  # 中间4个作为乡镇站
        'micro': df[~df['station_id'].str.contains('01|02|03|04|05|06|07')]  # 其余为微型站
    }


def generate_sample_spatial_events() -> list:
    """生成示例空间事件数据"""
    events = []
    
    # 建筑工地
    events.append(SpatialEvent(
        event_id='construction_001',
        event_type='construction',
        location=(120, 180, 0),
        start_time=(datetime.now() - timedelta(hours=2)).isoformat(),
        end_time=None,
        intensity=8.0,
        description='大型建筑工地土石方作业',
        responsible_unit='XX建筑公司'
    ))
    
    # 工业排放
    events.append(SpatialEvent(
        event_id='industrial_001',
        event_type='industrial',
        location=(80, 220, 15),
        start_time=(datetime.now() - timedelta(hours=1)).isoformat(),
        end_time=None,
        intensity=6.5,
        description='化工企业废气排放',
        responsible_unit='XX化工厂'
    ))
    
    # 交通拥堵
    events.append(SpatialEvent(
        event_id='traffic_001',
        event_type='traffic',
        location=(150, 200, 0),
        start_time=(datetime.now() - timedelta(minutes=30)).isoformat(),
        end_time=None,
        intensity=4.0,
        description='主干道交通拥堵',
        responsible_unit='交通管理部门'
    ))
    
    return events


def run_basic_example():
    """运行基础示例"""
    print("=== 基础污染溯源示例 ===\n")
    
    # 1. 创建系统配置
    config = SystemConfig(
        region_bounds=(-1000, 1000, -1000, 1000),
        grid_resolution=50,
        search_bounds={
            'x': (-500, 500),
            'y': (-500, 500),
            'z': (0, 30),
            'q': (0.1, 5.0)
        },
        ga_parameters=GAParameters(
            population_size=30,
            max_generations=500,
            crossover_rate=0.8,
            mutation_rate=0.1,
            elite_rate=0.2
        ),
        response_time_threshold=5.0
    )
    
    # 2. 初始化系统
    tracing_system = PollutionTracingSystem(config)
    
    # 3. 生成示例数据
    print("生成示例监测数据...")
    monitoring_data = generate_sample_monitoring_data(
        num_stations=8, 
        num_records_per_station=12,
        add_pollution_event=True
    )
    
    spatial_events = generate_sample_spatial_events()
    
    # 4. 执行溯源分析
    print("执行污染溯源分析...")
    result = tracing_system.process_real_time_data(
        monitoring_data_sources=monitoring_data,
        spatial_events=spatial_events,
        verbose=True
    )
    
    # 5. 显示结果
    print("\n=== 分析结果摘要 ===")
    print(f"分析时间: {result.timestamp}")
    print(f"计算用时: {result.computation_time:.2f}秒")
    print(f"数据质量: 总记录{result.data_quality_report.total_records}条")
    print(f"预警事件: {len(result.warning_events)}个")
    print(f"识别污染源: {len(result.inversion_results)}个")
    print(f"生成热力图: {len(result.heatmap_data)}个")
    print(f"关联分析: {len(result.correlation_results)}个")
    
    # 6. 详细结果
    if result.warning_events:
        print("\n预警事件详情:")
        for i, warning in enumerate(result.warning_events[:5]):  # 显示前5个
            print(f"  {i+1}. {warning.pollutant} {warning.warning_level.value} "
                  f"浓度:{warning.concentration:.1f} 站点:{warning.station_id}")
    
    if result.inversion_results:
        print("\n污染源反算结果:")
        for i, source in enumerate(result.inversion_results):
            print(f"  {i+1}. 位置:({source.source_x:.1f}, {source.source_y:.1f}, {source.source_z:.1f}) "
                  f"源强:{source.emission_rate:.3f}g/s 用时:{source.computation_time:.2f}s")
    
    if result.recommendations:
        print("\n系统建议:")
        for i, rec in enumerate(result.recommendations):
            print(f"  {i+1}. {rec}")
    
    # 7. 导出结果
    export_path = "溯源算法/example_result.json"
    tracing_system.export_results(result, export_path)
    print(f"\n结果已导出到: {export_path}")
    
    return result


def run_performance_test():
    """运行性能测试"""
    print("\n=== 性能测试 ===\n")
    
    # 测试不同规模的数据
    test_cases = [
        {'stations': 5, 'records': 6, 'name': '小规模'},
        {'stations': 10, 'records': 12, 'name': '中规模'},
        {'stations': 20, 'records': 24, 'name': '大规模'}
    ]
    
    config = SystemConfig(
        ga_parameters=GAParameters(
            population_size=30,
            max_generations=300
        )
    )
    
    tracing_system = PollutionTracingSystem(config)
    
    performance_results = []
    
    for test_case in test_cases:
        print(f"测试{test_case['name']}数据...")
        
        # 生成测试数据
        monitoring_data = generate_sample_monitoring_data(
            num_stations=test_case['stations'],
            num_records_per_station=test_case['records'],
            add_pollution_event=True
        )
        
        # 执行分析
        result = tracing_system.process_real_time_data(
            monitoring_data_sources=monitoring_data,
            verbose=False
        )
        
        performance_results.append({
            'name': test_case['name'],
            'stations': test_case['stations'],
            'records': test_case['records'],
            'computation_time': result.computation_time,
            'warnings': len(result.warning_events),
            'sources_found': len(result.inversion_results),
            'data_quality': result.system_performance.get('data_quality_score', 0)
        })
        
        print(f"  用时: {result.computation_time:.2f}秒")
        print(f"  预警: {len(result.warning_events)}个")
        print(f"  污染源: {len(result.inversion_results)}个")
    
    # 性能总结
    print("\n性能测试总结:")
    print("规模\t站点数\t记录数\t用时(秒)\t预警数\t污染源数\t数据质量")
    print("-" * 60)
    for perf in performance_results:
        print(f"{perf['name']}\t{perf['stations']}\t{perf['records']}\t"
              f"{perf['computation_time']:.2f}\t{perf['warnings']}\t"
              f"{perf['sources_found']}\t{perf['data_quality']:.3f}")
    
    return performance_results


def run_accuracy_test():
    """运行精度测试"""
    print("\n=== 精度测试 ===\n")
    
    # 设置已知污染源
    true_sources = [
        {'x': 100, 'y': 200, 'z': 5, 'q': 1.5},
        {'x': -150, 'y': 300, 'z': 10, 'q': 2.0}
    ]
    
    config = SystemConfig(
        ga_parameters=GAParameters(
            population_size=50,
            max_generations=800
        )
    )
    
    tracing_system = PollutionTracingSystem(config)
    
    accuracy_results = []
    
    for i, true_source in enumerate(true_sources):
        print(f"测试污染源 {i+1}: 位置({true_source['x']}, {true_source['y']}, {true_source['z']}) "
              f"源强{true_source['q']}g/s")
        
        # 生成基于真实污染源的数据
        monitoring_data = generate_sample_monitoring_data(
            num_stations=12,
            num_records_per_station=8,
            add_pollution_event=True
        )
        
        result = tracing_system.process_real_time_data(
            monitoring_data_sources=monitoring_data,
            verbose=False
        )
        
        if result.inversion_results:
            best_result = min(result.inversion_results, key=lambda x: x.objective_value)
            
            # 计算误差
            position_error = np.sqrt(
                (best_result.source_x - true_source['x'])**2 +
                (best_result.source_y - true_source['y'])**2 +
                (best_result.source_z - true_source['z'])**2
            )
            
            emission_error = abs(best_result.emission_rate - true_source['q']) / true_source['q'] * 100
            
            accuracy_results.append({
                'source_id': i+1,
                'true_position': (true_source['x'], true_source['y'], true_source['z']),
                'estimated_position': (best_result.source_x, best_result.source_y, best_result.source_z),
                'true_emission': true_source['q'],
                'estimated_emission': best_result.emission_rate,
                'position_error': position_error,
                'emission_error': emission_error,
                'computation_time': best_result.computation_time
            })
            
            print(f"  估算位置: ({best_result.source_x:.1f}, {best_result.source_y:.1f}, {best_result.source_z:.1f})")
            print(f"  估算源强: {best_result.emission_rate:.3f}g/s")
            print(f"  位置误差: {position_error:.1f}m")
            print(f"  源强误差: {emission_error:.1f}%")
            print(f"  计算用时: {best_result.computation_time:.2f}s")
        else:
            print("  未找到污染源")
    
    # 精度总结
    if accuracy_results:
        avg_position_error = np.mean([r['position_error'] for r in accuracy_results])
        avg_emission_error = np.mean([r['emission_error'] for r in accuracy_results])
        avg_computation_time = np.mean([r['computation_time'] for r in accuracy_results])
        
        print(f"\n精度测试总结:")
        print(f"平均位置误差: {avg_position_error:.1f}m")
        print(f"平均源强误差: {avg_emission_error:.1f}%")
        print(f"平均计算时间: {avg_computation_time:.2f}s")
        
        # 检查是否满足论文中的性能指标
        print(f"\n性能指标对比:")
        print(f"位置误差 ≤ 10m: {'✓' if avg_position_error <= 10 else '✗'}")
        print(f"源强误差 ≤ 7.2%: {'✓' if avg_emission_error <= 7.2 else '✗'}")
        print(f"计算时间 ≤ 2.44s: {'✓' if avg_computation_time <= 2.44 else '✗'}")
    
    return accuracy_results


if __name__ == "__main__":
    # 运行示例
    print("开始运行污染溯源系统示例...\n")
    
    # 基础示例
    basic_result = run_basic_example()
    
    # 性能测试
    performance_results = run_performance_test()
    
    # 精度测试
    accuracy_results = run_accuracy_test()
    
    print("\n=== 所有测试完成 ===")
    print("详细结果已保存到相应文件中。")
